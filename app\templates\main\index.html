<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.8/dist/chart.umd.min.js"></script>

  <!-- 配置Tailwind CSS -->
  <script nonce="{{ csp_nonce }}">
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36CBCB',
            accent: '#722ED1',
            dark: '#1D2129',
            light: '#F7F8FA',
            'primary-light': '#E8F3FF',
            'primary-dark': '#0D47A1',
            'neon-blue': '#00BFFF',
            'neon-purple': '#9D4EDD',
            'neon-green': '#00FF9D',
            'dark-blue': '#0B0E2F',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
            code: ['JetBrains Mono', 'monospace'],
          },
        },
      }
    }
  </script>

  <style type="text/tailwindcss" nonce="{{ csp_nonce }}">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .text-shadow {
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
      }
      .section-padding {
        @apply py-16 md:py-24;
      }
      .bg-gradient-primary {
        @apply bg-gradient-to-r from-primary to-primary-dark;
      }
      .animate-float {
        animation: float 6s ease-in-out infinite;
      }
      .neon-glow {
        box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
      }
      .neon-text {
        text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
      }
      .data-pulse {
        animation: pulse 2s infinite;
      }
      @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
      }
      @keyframes pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
        100% { transform: scale(1); opacity: 1; }
      }
      .bg-grid {
        background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                          linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
        background-size: 20px 20px;
      }
      .clip-path-slant {
        clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
      }
    }
  </style>
</head>

<body class="font-inter bg-dark-blue text-white antialiased overflow-x-hidden">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid z-0 opacity-50"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark-blue/80 backdrop-blur-md border-b border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16 md:h-20">
        <div class="flex items-center">
          <a href="#" class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fa fa-cutlery text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="text-white hover:text-neon-blue transition-colors font-medium">核心功能</a>
          <a href="#advantages" class="text-white hover:text-neon-blue transition-colors font-medium">系统优势</a>
          <a href="#process" class="text-white hover:text-neon-blue transition-colors font-medium">管理流程</a>
          <a href="#contact" class="text-white hover:text-neon-blue transition-colors font-medium">联系我们</a>
        </nav>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white hover:text-neon-blue focus:outline-none">
            <i class="fa fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden bg-dark-blue/95 backdrop-blur-md border-t border-primary/20">
      <div class="container mx-auto px-4 py-3 space-y-3">
        <a href="#features" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">核心功能</a>
        <a href="#advantages" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">系统优势</a>
        <a href="#process" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">管理流程</a>
        <a href="#contact" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-primary/5 to-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -left-40 w-80 h-80 bg-neon-blue/20 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-xl">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('auth.login') }}" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center neon-glow">
              立即登录
              <i class="fa fa-sign-in ml-2"></i>
            </a>
            <a href="{{ url_for('auth.register') }}" class="bg-transparent hover:bg-white/10 text-white border border-neon-green/50 font-medium px-8 py-3 rounded-lg transition-all flex items-center hover:border-neon-green">
              免费注册
              <i class="fa fa-user-plus ml-2"></i>
            </a>
            <a href="#features" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-6 py-3 rounded-lg transition-all flex items-center text-sm">
              了解功能
              <i class="fa fa-arrow-down ml-2"></i>
            </a>
          </div>

          <!-- 数据指标 -->
          <div class="mt-12 grid grid-cols-3 gap-4">
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-blue font-code text-2xl font-bold">99.9%</p>
              <p class="text-sm text-gray-400">系统稳定性</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-green font-code text-2xl font-bold">80%</p>
              <p class="text-sm text-gray-400">管理效率提升</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-purple font-code text-2xl font-bold">100%</p>
              <p class="text-sm text-gray-400">食品溯源率</p>
            </div>
          </div>
        </div>
        <div class="lg:w-1/2 relative">
          <div class="relative z-10 animate-float">
            <div class="bg-dark/50 backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30 overflow-hidden">
              <img src="https://picsum.photos/id/292/600/400" alt="智慧食堂管理系统界面" class="w-full h-auto">
              <div class="p-4 border-t border-primary/20">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-neon-green/20 rounded-full flex items-center justify-center text-neon-green">
                      <i class="fa fa-check text-lg"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-white">食品安全</p>
                      <p class="text-xs text-gray-400">全程可追溯</p>
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">实时监控中 <span class="inline-block w-2 h-2 bg-neon-green rounded-full animate-pulse ml-1"></span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 数据仪表盘 -->
  <section class="py-12 bg-dark/50 relative">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-dark/50 backdrop-blur-md rounded-xl border border-primary/20 p-6 shadow-lg">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 class="text-xl font-bold text-white mb-4 md:mb-0">系统运行数据 <span class="text-neon-blue text-sm font-normal">实时更新</span></h3>
          <div class="flex space-x-3">
            <button class="px-3 py-1 bg-primary/20 text-white text-sm rounded hover:bg-primary/30 transition-colors">今日</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本周</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本月</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 图表1 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食堂运营效率</h4>
            <div class="h-48">
              <canvas id="efficiencyChart"></canvas>
            </div>
          </div>

          <!-- 图表2 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食品安全检测</h4>
            <div class="h-48">
              <canvas id="safetyChart"></canvas>
            </div>
          </div>

          <!-- 图表3 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">用户满意度</h4>
            <div class="h-48">
              <canvas id="satisfactionChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全管理</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 功能卡片1 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-search text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
          <p class="text-gray-400">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
        </div>

        <!-- 功能卡片2 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-users text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
          <p class="text-gray-400">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
        </div>

        <!-- 功能卡片3 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-file-text-o text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
          <p class="text-gray-400">
            每日自动生成食堂工作日志，完整记录运营情况，基于数据进行分析，为管理决策提供依据
          </p>
        </div>

        <!-- 功能卡片4 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fa fa-list text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
          <p class="text-gray-400">
            支持周菜单灵活安排与调整，可直接打印输出