// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 激活当前页面对应的导航项
    activateCurrentNavItem();

    // 自动关闭警告消息
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化日期选择器
    initDatepickers();

    // 初始化通知系统
    initNotificationSystem();
});

// 激活当前页面对应的导航项
function activateCurrentNavItem() {
    const currentPath = window.location.pathname;

    // 处理下拉菜单项
    const dropdownItems = document.querySelectorAll('.dropdown-menu .dropdown-item');
    let activeDropdownFound = false;

    dropdownItems.forEach(function(item) {
        const href = item.getAttribute('href');
        // 检查路径是否匹配或者是子路径
        if (href && (href === currentPath || currentPath.startsWith(href + '/'))) {
            item.classList.add('active');
            activeDropdownFound = true;

            // 激活父菜单
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                const dropdownToggle = dropdownMenu.previousElementSibling;
                if (dropdownToggle && dropdownToggle.classList.contains('dropdown-toggle')) {
                    dropdownToggle.classList.add('active');
                }
            }
        }
    });

    // 如果没有找到活动的下拉菜单项，检查顶级导航项
    if (!activeDropdownFound) {
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(function(link) {
            const href = link.getAttribute('href');
            if (href && href !== '#' && (href === currentPath || currentPath.startsWith(href + '/'))) {
                link.classList.add('active');
            }
        });
    }
}

// 初始化通知系统
function initNotificationSystem() {
    // 检查是否已登录（通过检查通知图标是否存在）
    const notificationDropdown = document.getElementById('notificationDropdown');
    if (!notificationDropdown) return;

    // 首次加载页面时检查通知
    checkNotifications();

    // 每60秒检查一次新通知
    setInterval(checkNotifications, 60000);
}

// 检查新通知
function checkNotifications() {
    fetch('/notifications/check')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            updateNotificationBadge(data.unread_count);
            updateNotificationList(data.notifications);
        })
        .catch(error => {
            // 静默处理错误，避免控制台噪音
            if (window.console && window.console.debug) {
                console.debug('获取通知失败:', error);
            }
        });
}

// 更新通知图标上的未读数量
function updateNotificationBadge(count) {
    const badge = document.getElementById('notification-badge');

    if (count > 0) {
        if (badge) {
            badge.textContent = count;
        } else {
            const notificationIcon = document.getElementById('notificationDropdown');
            const newBadge = document.createElement('span');
            newBadge.id = 'notification-badge';
            newBadge.className = 'badge badge-danger notification-badge';
            newBadge.textContent = count;
            notificationIcon.appendChild(newBadge);
        }
    } else if (badge) {
        badge.remove();
    }
}

// 更新通知下拉菜单中的通知列表
function updateNotificationList(notifications) {
    const notificationList = document.getElementById('notification-list');
    if (!notificationList) return;

    if (notifications && notifications.length > 0) {
        let html = '';

        notifications.forEach(notification => {
            html += `
                <a class="dropdown-item notification-item ${notification.is_read ? '' : 'unread'}" href="/notifications/view/${notification.id}">
                    <div class="notification-title">
                        ${notification.level === 2 ? '<span class="badge badge-danger">紧急</span>' :
                          notification.level === 1 ? '<span class="badge badge-warning">重要</span>' : ''}
                        ${notification.title}
                    </div>
                    <div class="notification-content">${notification.content}</div>
                    <div class="notification-time">${notification.created_at}</div>
                </a>
            `;
        });

        notificationList.innerHTML = html;
    } else {
        notificationList.innerHTML = '<div class="dropdown-item text-center">暂无通知</div>';
    }
}

// 初始化日期选择器
function initDatepickers() {
    // 查找所有带有 datepicker 类的输入框
    const datepickers = document.querySelectorAll('.datepicker');
    if (datepickers.length > 0 && $.fn.datepicker) {
        datepickers.forEach(function(input) {
            $(input).datepicker({
                language: 'zh-CN',
                format: 'yyyy-mm-dd',
                autoclose: true,
                todayHighlight: true,
                clearBtn: true
            });
        });
    }

    // 查找所有带有 daterangepicker 类的输入框
    const daterangepickers = document.querySelectorAll('.daterangepicker');
    if (daterangepickers.length > 0 && $.fn.daterangepicker) {
        daterangepickers.forEach(function(input) {
            $(input).daterangepicker({
                locale: {
                    format: 'YYYY-MM-DD',
                    separator: ' 至 ',
                    applyLabel: '确定',
                    cancelLabel: '取消',
                    fromLabel: '从',
                    toLabel: '到',
                    customRangeLabel: '自定义',
                    weekLabel: '周',
                    daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
                    monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                    firstDay: 1
                }
            });
        });
    }
}

// 全局错误处理器
window.addEventListener('unhandledrejection', function(event) {
    // 静默处理未捕获的Promise错误，避免控制台噪音
    if (window.console && window.console.debug) {
        console.debug('未处理的Promise错误:', event.reason);
    }
    // 阻止默认的错误处理
    event.preventDefault();
});

// 全局错误处理器
window.addEventListener('error', function(event) {
    // 静默处理某些已知的无害错误
    const message = event.message || '';
    if (message.includes('runtime.lastError') ||
        message.includes('Extension context invalidated') ||
        message.includes('chrome-extension://')) {
        // 这些通常是浏览器扩展相关的错误，可以安全忽略
        if (window.console && window.console.debug) {
            console.debug('浏览器扩展相关错误（已忽略）:', message);
        }
        event.preventDefault();
        return true;
    }
});

// 页面加载完成后初始化
$(document).ready(function() {
    initNotifications();
    initDatepickers();
});