{% extends 'base.html' %}

{% block title %}入库单详情{% endblock %}

{% block extra_css %}
<style>
    /* 文档表格样式优化 */
    .document-table {
        table-layout: fixed;
        width: 100%;
    }

    .document-table th,
    .document-table td {
        vertical-align: middle;
    }

    .filename-cell {
        word-break: break-all;
        word-wrap: break-word;
        max-width: 150px;
        min-width: 120px;
        line-height: 1.4;
        white-space: normal;
        overflow-wrap: break-word;
        hyphens: auto;
    }

    .filename-text {
        display: block;
        font-size: 0.9em;
        word-break: break-all;
        overflow-wrap: break-word;
    }

    .action-buttons {
        white-space: nowrap;
        min-width: 200px;
    }

    .action-buttons .btn {
        margin: 1px 2px;
        font-size: 0.75em;
        padding: 0.2rem 0.4rem;
        display: inline-block;
    }

    /* 文档操作按钮样式 */
    .action-buttons .document-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 3px;
        align-items: center;
        justify-content: flex-start;
        max-width: 100px; /* 限制最大宽度，确保自动换行 */
    }

    .action-buttons .document-actions .btn {
        width: 28px;
        height: 28px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        font-size: 12px;
        border-width: 1px;
        margin: 0;
        flex-shrink: 0; /* 防止按钮被压缩 */
    }

    .action-buttons .document-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 响应式调整 */
    @media (max-width: 1200px) {
        .action-buttons .document-actions {
            max-width: 90px;
        }
    }

    @media (max-width: 992px) {
        .action-buttons .document-actions {
            max-width: 80px;
        }
    }

    /* 工作流进度样式 */
    .workflow-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 28px;
        color: white;
        margin: 0 auto 15px auto;
        position: relative;
    }

    .workflow-circle.active {
        background-color: #007bff;
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    .workflow-circle.completed {
        background-color: #28a745;
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }

    .workflow-circle.inactive {
        background-color: #6c757d;
        box-shadow: 0 2px 4px rgba(108, 117, 125, 0.2);
    }

    .workflow-step-container {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .workflow-arrow {
        position: absolute;
        right: -25px;
        top: 40px;
        font-size: 24px;
        color: #6c757d;
        z-index: 1;
    }

    @media (max-width: 768px) {
        .filename-cell {
            max-width: 150px;
        }

        .action-buttons {
            white-space: normal;
        }

        .action-buttons .btn {
            display: block;
            width: 100%;
            margin-bottom: 2px;
        }
    }

    /* 文档查看模态框样式 */
    .modal-xl {
        max-width: 95%;
    }

    .document-preview-panel {
        min-height: 70vh;
    }

    .document-info-panel {
        background-color: #f8f9fa;
        border-right: 1px solid #dee2e6;
    }

    .document-info-panel .badge-sm {
        font-size: 0.75em;
        padding: 0.25em 0.5em;
    }

    .document-preview img {
        max-width: 100%;
        height: auto;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .document-preview iframe {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .file-type-icon {
        color: #6c757d;
        margin-bottom: 1rem;
    }

    @media (max-width: 768px) {
        .modal-xl {
            max-width: 98%;
            margin: 0.5rem;
        }

        .document-info-panel {
            border-right: none;
            border-bottom: 1px solid #dee2e6;
        }

        .document-preview-panel {
            min-height: 50vh;
        }
    }

    /* 工作流进度条样式 */
    .progress-workflow {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px 0;
    }

    .workflow-step {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        min-width: 200px;
        position: relative;
    }

    .workflow-step.active .step-icon {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .workflow-step.completed .step-icon {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }

    .workflow-step .step-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: 3px solid #dee2e6;
        background-color: #f8f9fa;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        margin-bottom: 10px;
        transition: all 0.3s ease;
    }

    .workflow-step .step-content h6 {
        margin-bottom: 5px;
        font-weight: bold;
    }

    .workflow-step .step-content p {
        margin: 0;
        color: #6c757d;
    }

    .workflow-arrow {
        font-size: 24px;
        color: #dee2e6;
        margin: 0 20px;
        font-weight: bold;
    }

    .workflow-step.active .step-content h6,
    .workflow-step.completed .step-content h6 {
        color: #495057;
    }

    .workflow-step.active .step-content p,
    .workflow-step.completed .step-content p {
        color: #6c757d;
    }

    @media (max-width: 768px) {
        .progress-workflow {
            flex-direction: column;
        }

        .workflow-arrow {
            transform: rotate(90deg);
            margin: 10px 0;
        }

        .workflow-step {
            min-width: auto;
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid stock-in-detail-page">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <h3 class="card-title mb-0 mr-3">入库单详情</h3>
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <button type="button" class="btn btn-outline-primary btn-sm" onclick="printStockIn({{ stock_in.id }})">
                            <i class="fas fa-print"></i> 打印
                        </button>
                    </div>
                    <div class="card-tools">
                        {% if stock_in.status == '待审核' %}
                        <a href="{{ url_for('stock_in.edit', id=stock_in.id) }}" class="btn btn-primary btn-sm mr-1">
                            <i class="fas fa-edit"></i> 编辑
                        </a>
                        <a href="{{ url_for('stock_in.batch_editor_simplified', id=stock_in.id) }}" class="btn btn-info btn-sm mr-1">
                            <i class="fas fa-th-list"></i> 批次编辑器
                        </a>
                        <a href="{{ url_for('stock_in.approve', id=stock_in.id) }}" class="btn btn-success btn-sm" onclick="return confirm('确定要审核此入库单吗？审核后需要点击确认入库才会更新库存。')">
                            <i class="fas fa-check-circle"></i> 审核入库单
                        </a>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">入库单号</th>
                                    <td>{{ stock_in.stock_in_number }}</td>
                                </tr>
                                <tr>
                                    <th>仓库</th>
                                    <td>{{ stock_in.warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>入库日期</th>
                                    <td>{{  stock_in.stock_in_date|format_datetime('%Y-%m-%d')  }}</td>
                                </tr>
                                <tr>
                                    <th>入库类型</th>
                                    <td>{{ stock_in.stock_in_type }}</td>
                                </tr>
                                <tr>
                                    <th>供应商</th>
                                    <td>{{ stock_in.supplier.name if stock_in.supplier else '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 30%">操作人</th>
                                    <td>{{ stock_in.operator.real_name or stock_in.operator.username }}</td>
                                </tr>
                                <tr>
                                    <th>审批人</th>
                                    <td>{{ stock_in.inspector.real_name or stock_in.inspector.username if stock_in.inspector else '-' }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if stock_in.status == '待审核' %}
                                        <span class="badge badge-warning">待审核</span>
                                        {% elif stock_in.status == '已审核' %}
                                        <span class="badge badge-info">已审核</span>
                                        {% elif stock_in.status == '已入库' %}
                                        <span class="badge badge-success">已入库</span>
                                        {% elif stock_in.status == '已取消' %}
                                        <span class="badge badge-danger">已取消</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{  stock_in.created_at|format_datetime('%Y-%m-%d %H:%M:%S')  }}</td>
                                </tr>
                                <tr>
                                    <th>备注</th>
                                    <td>{{ stock_in.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 入库工作流进度 -->
                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-tasks"></i> 入库工作流进度</h5>
                        </div>
                        <div class="card-body">
                            <!-- 工作流步骤 -->
                            <div class="row text-center mb-4">
                                <div class="col-md-4">
                                    <div class="workflow-step-container">
                                        <div class="workflow-circle completed">
                                            <i class="fas fa-edit"></i>
                                        </div>
                                        <div class="workflow-arrow d-none d-md-block">→</div>
                                    </div>
                                    <h6>步骤1: 编辑批次信息</h6>
                                    <p class="text-muted small">设置供应商、存储位置、数量单价等</p>
                                </div>
                                <div class="col-md-4">
                                    <div class="workflow-step-container">
                                        <div class="workflow-circle {% if stock_in.status == '已入库' %}completed{% elif stock_in.status == '待审核' %}active{% else %}inactive{% endif %}">
                                            <i class="fas fa-warehouse"></i>
                                        </div>
                                        <div class="workflow-arrow d-none d-md-block">→</div>
                                    </div>
                                    <h6>步骤2: 确认入库</h6>
                                    <p class="text-muted small">将数据写入学校库存系统</p>
                                </div>
                                <div class="col-md-4">
                                    <div class="workflow-step-container">
                                        <div class="workflow-circle {% if stock_in.status == '已审核' %}completed{% elif stock_in.status == '已入库' %}active{% else %}inactive{% endif %}">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                    </div>
                                    <h6>步骤3: 审核通过</h6>
                                    <p class="text-muted small">最终确认和审核批准</p>
                                </div>
                            </div>

                            <!-- 当前状态提示 -->
                            {% if stock_in.status == '待审核' %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>当前状态：待审核</strong> - 请点击"确认入库"按钮将数据写入学校库存系统
                            </div>
                            {% elif stock_in.status == '已入库' %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>当前状态：已入库</strong> - 数据已写入学校库存系统，请点击"审核通过"完成最终审核
                            </div>
                            {% elif stock_in.status == '已审核' %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i>
                                <strong>当前状态：已审核</strong> - 入库流程已完成，数据已保存到学校库存系统
                            </div>
                            {% elif stock_in.status == '已取消' %}
                            <div class="alert alert-secondary">
                                <i class="fas fa-ban"></i>
                                <strong>当前状态：已取消</strong> - 该入库单已被取消
                            </div>
                            {% endif %}

                            <!-- 操作提示 -->
                            {% if stock_in.status == '待审核' %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>操作提示</strong> - 新的入库流程：先确认入库（写入库存），再审核通过（最终确认）
                            </div>
                            {% elif stock_in.status == '已入库' %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>操作提示</strong> - 库存数据已写入，请进行最终审核确认。如有问题可撤销入库重新操作。
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 入库明细列表 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">入库明细列表</h4>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped stock-in-items-table">
                                    <thead>
                                        <tr>
                                            <th>序号</th>
                                            <th>食材名称</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>单价</th>
                                            <th>供应商</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_in_items %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>
                                                <a href="{{ url_for('stock_in_detail.view', item_id=item.id) }}" title="查看食材详情">
                                                    {{ item.ingredient.name }}
                                                </a>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('stock_in_detail.view', item_id=item.id) }}" title="查看批次详情">
                                                    {{ item.batch_number }}
                                                </a>
                                            </td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{ item.unit_price or '-' }}</td>
                                            <td>{{ item.supplier.name if item.supplier else '-' }}</td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="7" class="text-center">暂无入库明细</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- 入库单据管理 -->
                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h4 class="card-title"><i class="fas fa-file-alt"></i> 入库单据管理</h4>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 入库单据包括送货单、检验检疫证明、质量检测报告等文档，用于食材质量追溯和管理。
                            </div>

                            {% if stock_in.status == '已审核' %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 此入库单已审核，请点击"确认入库"按钮完成入库流程。确认后将更新库存，此操作不可撤销。
                            </div>
                            {% endif %}

                            <div class="table-responsive">
                                <table class="table table-bordered table-hover document-table">
                                    <thead>
                                        <tr>
                                            <th style="width: 12%;">文档类型</th>
                                            <th style="width: 18%;">文件名</th>
                                            <th style="width: 15%;">上传时间</th>
                                            <th style="width: 12%;">关联供应商</th>
                                            <th style="width: 16%;">关联食材</th>
                                            <th style="width: 27%;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for doc in documents %}
                                        <tr>
                                            <td>
                                                <span class="badge badge-{{ 'warning' if doc.document_type in ['检验检疫证明', '质量检测报告'] else 'info' }}">
                                                    {{ doc.document_type }}
                                                </span>
                                            </td>
                                            <td class="filename-cell">
                                                <span class="filename-text text-primary" title="{{ doc.file_path }}">
                                                    {{ doc.file_path|filename }}
                                                </span>
                                            </td>
                                            <td>{{ doc.created_at|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ doc.supplier.name if doc.supplier else '-' }}</td>
                                            <td>
                                                {% if doc.items %}
                                                <button type="button" class="btn btn-sm btn-outline-info" data-toggle="modal" data-target="#docItemsModal{{ doc.id }}">
                                                    查看关联食材 ({{ doc.items|length }})
                                                </button>
                                                {% else %}
                                                <span class="text-muted">无关联食材</span>
                                                {% endif %}
                                            </td>
                                            <td class="action-buttons">
                                                <div class="document-actions">
                                                    <button type="button" class="btn btn-outline-primary" data-toggle="modal" data-target="#viewDocModal{{ doc.id }}" title="查看文档">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-outline-success" title="下载文档">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                    {% if stock_in.status == '待审核' %}
                                                    <button type="button" class="btn btn-outline-danger" onclick="deleteDocument({{ doc.id }})" title="删除文档">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                    {% endif %}
                                                </div>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">暂无上传文档</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            {% if stock_in.status == '待审核' %}
                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#uploadDocumentModal">
                                    <i class="fas fa-upload"></i> 上传新文档
                                </button>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12 text-center stock-in-actions">
                            {% if stock_in.status == '待审核' %}
                            <!-- 待审核状态：显示入库操作和取消入库单按钮 -->
                            <button type="button" class="btn btn-primary btn-lg mr-3" id="stockInBtn" {% if not stock_in_items %}disabled{% endif %}>
                                <i class="fas fa-warehouse"></i> 确认入库
                            </button>
                            <form action="{{ url_for('stock_in.cancel', id=stock_in.id) }}" method="post" style="display: inline;">
                                <button type="submit" class="btn btn-danger btn-lg" onclick="return confirm('确定要取消该入库单吗？')">
                                    <i class="fas fa-times"></i> 取消入库单
                                </button>
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            </form>

                            {% elif stock_in.status == '已入库' %}
                            <!-- 已入库状态：显示审核通过按钮 -->
                            <form action="{{ url_for('stock_in.approve', id=stock_in.id) }}" method="post" style="display: inline;">
                                <button type="submit" class="btn btn-success btn-lg mr-3">
                                    <i class="fas fa-check"></i> 审核通过
                                </button>
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            </form>
                            <button type="button" class="btn btn-warning btn-lg mr-3" id="cancelStockInBtn">
                                <i class="fas fa-undo"></i> 撤销入库
                            </button>
                            <form action="{{ url_for('stock_in.cancel', id=stock_in.id) }}" method="post" style="display: inline;">
                                <button type="submit" class="btn btn-danger btn-lg" onclick="return confirm('确定要取消该入库单吗？')">
                                    <i class="fas fa-times"></i> 取消入库单
                                </button>
                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            </form>

                            {% elif stock_in.status == '已审核' %}
                            <!-- 已审核状态：显示完成提示 -->
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> 该入库单已完成审核，流程结束
                            </div>

                            {% elif stock_in.status == '已取消' %}
                            <!-- 已取消状态：显示提示信息 -->
                            <div class="alert alert-secondary">
                                <i class="fas fa-ban"></i> 该入库单已被取消
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 文档上传模态框 -->
<div class="modal fade" id="uploadDocumentModal" tabindex="-1" role="dialog" aria-labelledby="uploadDocumentModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="uploadDocumentModalLabel"><i class="fas fa-file-upload"></i> 上传入库单据</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="uploadDocumentForm" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="document_type">单据类型 <span class="text-danger">*</span></label>
                        <select class="form-control" id="document_type" name="document_type" required>
                            <option value="送货单">送货单</option>
                            <option value="检验检疫证明">检验检疫证明</option>
                            <option value="质量检测报告">质量检测报告</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="document_supplier_id">关联供应商</label>
                        <select class="form-control" id="document_supplier_id" name="supplier_id">
                            <option value="">-- 请选择供应商 --</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="batch_numbers">关联食材批次号</label>
                        <textarea class="form-control" id="batch_numbers" name="batch_numbers" rows="3"
                                  placeholder="请输入要关联的批次号，每行一个批次号。例如：&#10;B20250127001&#10;B20250127002"></textarea>
                        <small class="form-text text-muted">
                            <i class="fas fa-info-circle"></i>
                            输入要关联的食材批次号，每行一个。系统会自动匹配该入库单中的对应批次。
                        </small>

                        <!-- 显示当前入库单的批次号 -->
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="showBatchNumbers()">
                                <i class="fas fa-list"></i> 查看当前入库单的批次号
                            </button>
                        </div>

                        <!-- 批次号列表（隐藏） -->
                        <div id="batchNumbersList" class="mt-2" style="display: none;">
                            <div class="alert alert-info">
                                <strong>当前入库单的批次号：</strong><br>
                                {% for item in stock_in_items %}
                                <span class="badge badge-secondary mr-1 mb-1" onclick="addBatchNumber('{{ item.batch_number }}')">
                                    {{ item.ingredient.name }}: {{ item.batch_number }}
                                </span>
                                {% endfor %}
                                <br><small class="text-muted">点击批次号可快速添加到关联列表</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="document">选择文件 <span class="text-danger">*</span></label>
                        <div class="custom-file">
                            <input type="file" class="custom-file-input" id="document" name="document" required>
                            <label class="custom-file-label" for="document">选择文件...</label>
                        </div>
                        <small class="form-text text-muted">支持的文件格式：PDF, PNG, JPG, JPEG, DOC, DOCX, XLS, XLSX</small>
                    </div>

                    <div class="form-group">
                        <label for="document_notes">备注</label>
                        <textarea class="form-control" id="document_notes" name="notes" rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-upload"></i> 上传文档
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 文档查看模态框 -->
{% for doc in documents %}
<div class="modal fade" id="viewDocModal{{ doc.id }}" tabindex="-1" role="dialog" aria-labelledby="viewDocModalLabel{{ doc.id }}" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="viewDocModalLabel{{ doc.id }}">
                    <i class="fas fa-file-alt"></i> 查看文档 - {{ doc.document_type }}
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body p-0">
                <div class="row no-gutters">
                    <!-- 文档信息面板 -->
                    <div class="col-md-3 document-info-panel">
                        <div class="p-3">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-info-circle"></i> 文档信息
                            </h6>

                            <div class="mb-3">
                                <small class="text-muted">文档类型</small>
                                <div>
                                    <span class="badge badge-{{ 'warning' if doc.document_type in ['检验检疫证明', '质量检测报告'] else 'info' }}">
                                        {{ doc.document_type }}
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">文件名</small>
                                <div class="small text-break">{{ doc.file_path.split('/')[-1] }}</div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">上传时间</small>
                                <div class="small">{{ doc.created_at|format_datetime('%Y-%m-%d %H:%M') }}</div>
                            </div>

                            <div class="mb-3">
                                <small class="text-muted">关联供应商</small>
                                <div class="small">{{ doc.supplier.name if doc.supplier else '无' }}</div>
                            </div>

                            {% if doc.items %}
                            <div class="mb-3">
                                <small class="text-muted">关联食材</small>
                                <div class="small">
                                    {% for item in doc.items %}
                                    <span class="badge badge-secondary badge-sm mr-1 mb-1">
                                        {{ item.ingredient.name }}
                                    </span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            {% if doc.notes %}
                            <div class="mb-3">
                                <small class="text-muted">备注</small>
                                <div class="small">{{ doc.notes }}</div>
                            </div>
                            {% endif %}

                            <div class="mt-4">
                                <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-success btn-sm btn-block">
                                    <i class="fas fa-download"></i> 下载文档
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- 文档预览面板 -->
                    <div class="col-md-9 document-preview-panel">
                        <div class="p-3">
                            <div class="text-center document-preview">
                                {% set filename = doc.file_path|filename %}
                                {% set file_ext = filename.split('.')[-1].lower() if '.' in filename else 'unknown' %}
                                {% if file_ext in ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'] %}
                                <!-- 图片预览 -->
                                <img src="{{ url_for('static', filename=doc.file_path|fix_path) }}"
                                     class="img-fluid"
                                     style="max-height: 70vh; border: 1px solid #ddd; border-radius: 4px;"
                                     alt="文档预览">
                                {% elif file_ext == 'pdf' %}
                                <!-- PDF预览 -->
                                <iframe src="{{ url_for('static', filename=doc.file_path|fix_path) }}"
                                        style="width: 100%; height: 70vh; border: 1px solid #ddd; border-radius: 4px;"
                                        frameborder="0">
                                    <p>您的浏览器不支持PDF预览。<a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" target="_blank">点击这里下载文档</a></p>
                                </iframe>
                                {% else %}
                                <!-- 其他文件类型 -->
                                <div class="text-center py-5">
                                    <div class="mb-4 file-type-icon">
                                        <i class="fas fa-file-alt fa-5x"></i>
                                    </div>
                                    <h5 class="text-muted">无法预览此文件类型</h5>
                                    <p class="text-muted">
                                        文件名：{{ filename }}<br>
                                        文件类型：{{ file_ext.upper() if file_ext != 'unknown' else '未知' }}<br>
                                        请下载文件后使用相应软件打开
                                    </p>
                                    <a href="{{ url_for('static', filename=doc.file_path|fix_path) }}" download class="btn btn-primary">
                                        <i class="fas fa-download"></i> 下载文档
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                {% if stock_in.status == '待审核' %}
                <button type="button" class="btn btn-danger" onclick="deleteDocument({{ doc.id }})">
                    <i class="fas fa-trash"></i> 删除文档
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- 文档关联食材模态框 -->
{% for doc in documents %}
<div class="modal fade" id="docItemsModal{{ doc.id }}" tabindex="-1" role="dialog" aria-labelledby="docItemsModalLabel{{ doc.id }}" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="docItemsModalLabel{{ doc.id }}"><i class="fas fa-link"></i> 关联食材 - {{ doc.document_type }}</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead>
                            <tr>
                                <th>食材名称</th>
                                <th>批次号</th>
                                <th>数量</th>
                                <th>单位</th>
                                <th>供应商</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in doc.items %}
                            <tr>
                                <td>{{ item.ingredient.name }}</td>
                                <td>{{ item.batch_number }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.unit }}</td>
                                <td>{{ item.supplier.name if item.supplier else '-' }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无关联食材</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if stock_in.status == '待审核' %}
                <div class="mt-3">
                    <button type="button" class="btn btn-primary" onclick="showAssociateItemsModal({{ doc.id }})">
                        <i class="fas fa-plus"></i> 添加关联食材
                    </button>
                </div>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endfor %}

<!-- 关联食材选择模态框 -->
<div class="modal fade" id="associateItemsModal" tabindex="-1" role="dialog" aria-labelledby="associateItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="associateItemsModalLabel"><i class="fas fa-link"></i> 选择关联食材</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 请选择要与该文档关联的食材。
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>选择</th>
                                <th>食材</th>
                                <th>批次号</th>
                                <th>数量</th>
                                <th>单位</th>
                            </tr>
                        </thead>
                        <tbody id="associateItemsList">
                            {% for item in stock_in_items %}
                            <tr>
                                <td>
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input select-item-for-document" id="associate_item_{{ item.id }}" data-id="{{ item.id }}">
                                        <label class="custom-control-label" for="associate_item_{{ item.id }}"></label>
                                    </div>
                                </td>
                                <td>{{ item.ingredient.name }}</td>
                                <td>{{ item.batch_number }}</td>
                                <td>{{ item.quantity }}</td>
                                <td>{{ item.unit }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <input type="hidden" id="selectedDocumentId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAssociationBtn">保存关联</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/stock-in-detail-enhancement.js') }}?v=1.0.0"></script>
{{ super() }}
<script nonce="{{ csp_nonce }}">
    function printStockIn(stockInId) {
        window.open("{{ url_for('stock_in.print_stock_in', id=0) }}".replace('0', stockInId), '_blank');
    }

    // 显示关联食材选择模态框
    function showAssociateItemsModal(documentId) {
        $('#selectedDocumentId').val(documentId);
        $('#docItemsModal' + documentId).modal('hide');
        $('#associateItemsModal').modal('show');
    }

    // 保存文档与食材的关联
    $('#saveAssociationBtn').click(function() {
        const documentId = $('#selectedDocumentId').val();
        const selectedItems = [];

        $('.select-item-for-document:checked').each(function() {
            selectedItems.push($(this).data('id'));
        });

        if (selectedItems.length === 0) {
            alert('请至少选择一项食材');
            return;
        }

        $.ajax({
            url: `/stock-in/associate-document/${documentId}`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ item_ids: selectedItems }),
            success: function(response) {
                if (response.success) {
                    alert('关联成功');
                    $('#associateItemsModal').modal('hide');
                    // 刷新页面以显示更新后的关联
                    location.reload();
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 上传文档
    $('#uploadDocumentForm').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: `/stock-in/{{ stock_in.id }}/upload-document`,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // 显示成功消息，包括关联信息
                    alert(response.message);
                    $('#uploadDocumentModal').modal('hide');

                    // 刷新页面以显示新上传的文档
                    location.reload();
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 确认入库按钮点击事件（新的入库流程）
    $('#stockInBtn').click(function() {
        if (confirm('确认入库将更新学校库存系统，此操作不可撤销，确定要继续吗？')) {
            $.ajax({
                url: "{{ url_for('stock_in.stock_in_operation', id=stock_in.id) }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert('错误: ' + response.message);
                    }
                },
                error: function() {
                    alert('服务器错误，请稍后重试');
                }
            });
        }
    });

    // 撤销入库按钮点击事件
    $('#cancelStockInBtn').click(function() {
        if (confirm('撤销入库将从学校库存系统中移除相关记录，确定要继续吗？')) {
            $.ajax({
                url: "{{ url_for('stock_in.cancel_stock_in_operation', id=stock_in.id) }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert('错误: ' + response.message);
                    }
                },
                error: function() {
                    alert('服务器错误，请稍后重试');
                }
            });
        }
    });

    // 删除文档
    function deleteDocument(documentId) {
        if (confirm('确定要删除该文档吗？')) {
            $.ajax({
                url: `/stock-in/delete-document/${documentId}`,
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert('文档删除成功');
                        location.reload();
                    } else {
                        alert('错误: ' + response.message);
                    }
                },
                error: function() {
                    alert('服务器错误，请稍后重试');
                }
            });
        }
    }

    // 自定义文件输入框显示文件名
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).next('.custom-file-label').html(fileName);
    });

    // 显示/隐藏批次号列表
    function showBatchNumbers() {
        $('#batchNumbersList').toggle();
    }

    // 添加批次号到文本框
    function addBatchNumber(batchNumber) {
        const textarea = $('#batch_numbers');
        const currentValue = textarea.val().trim();

        // 检查是否已经存在该批次号
        const lines = currentValue.split('\n').map(line => line.trim()).filter(line => line);
        if (lines.includes(batchNumber)) {
            alert('该批次号已经在列表中');
            return;
        }

        // 添加批次号
        if (currentValue) {
            textarea.val(currentValue + '\n' + batchNumber);
        } else {
            textarea.val(batchNumber);
        }

        // 高亮显示文本框
        textarea.focus();
    }
</script>
{% endblock %}
