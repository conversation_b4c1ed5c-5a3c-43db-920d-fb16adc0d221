{% extends 'base.html' %}

{% block title %}创建采购订单 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
  .ingredient-row {
    margin-bottom: 10px;
    padding: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #ffffff;
  }
  .ingredient-row:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
  }
  .supplier-select {
    width: 100%;
  }
  .quantity-input, .unit-price-input {
    width: 100%;
  }

  /* 修复颜色问题，使用系统主题 */
  .card-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .card-header h6 {
    color: #495057 !important;
  }

  .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: #ffffff;
  }

  .btn-primary:hover {
    background-color: #0056b3;
    border-color: #004085;
  }

  .form-label {
    color: #495057;
    font-weight: 500;
  }

  .text-primary {
    color: #007bff !important;
  }

  /* 隐藏区域选择字段 */
  .area-field-hidden {
    display: none;
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>创建采购订单</h2>
      <p class="text-muted">填写采购订单信息并提交</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
      </a>
    </div>
  </div>

  <div class="card shadow mb-4">
    <div class="card-header py-3">
      <h6 class="m-0 font-weight-bold text-primary">采购订单信息</h6>
    </div>
    <div class="card-body">
      <form method="post" id="purchase-order-form">
        {{ form.csrf_token }}

        <!-- 隐藏区域选择，自动绑定用户学校 -->
        <div class="area-field-hidden">
          {{ form.area_id() }}
        </div>

        <!-- 显示学校信息 -->
        <div class="row">
          <div class="col-md-6">
            <div class="form-group">
              <label class="form-label">所属学校</label>
              <input type="text" class="form-control" value="{{ user_area.name }}" readonly>
              <small class="form-text text-muted">采购订单将自动绑定到您的学校</small>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              {{ form.supplier_id.label(class="form-label") }}
              {{ form.supplier_id(class="form-control") }}
              {% if form.supplier_id.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.supplier_id.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">默认为自购，也可选择合作供应商</small>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-4">
            <div class="form-group">
              {{ form.order_date.label(class="form-label") }}
              {{ form.order_date(class="form-control", type="date") }}
              {% if form.order_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.order_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              {{ form.expected_delivery_date.label(class="form-label") }}
              {{ form.expected_delivery_date(class="form-control", type="date") }}
              {% if form.expected_delivery_date.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.expected_delivery_date.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
            </div>
          </div>
          <div class="col-md-4">
            <div class="form-group">
              {{ form.batch_number.label(class="form-label") }}
              {{ form.batch_number(class="form-control", readonly=true) }}
              {% if form.batch_number.errors %}
                <div class="invalid-feedback d-block">
                  {% for error in form.batch_number.errors %}
                    {{ error }}
                  {% endfor %}
                </div>
              {% endif %}
              <small class="form-text text-muted">系统自动生成</small>
            </div>
          </div>
        </div>

        <div class="form-group">
          {{ form.notes.label(class="form-label") }}
          {{ form.notes(class="form-control", rows=3) }}
          {% if form.notes.errors %}
            <div class="invalid-feedback d-block">
              {% for error in form.notes.errors %}
                {{ error }}
              {% endfor %}
            </div>
          {% endif %}
        </div>

        <div class="card shadow mb-4">
          <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">采购明细</h6>
            <button type="button" class="btn btn-sm btn-primary" id="add-ingredient">
              <i class="fas fa-plus"></i> 添加食材
            </button>
          </div>
          <div class="card-body">
            <div id="ingredients-container">
              <!-- 默认不显示任何食材行，用户点击添加按钮后才显示 -->
              <div id="no-ingredients-message" class="text-center text-muted py-4">
                <i class="fas fa-plus-circle fa-3x mb-3"></i>
                <p>暂无食材，请点击"添加食材"按钮开始添加</p>
              </div>
            </div>

            <div class="text-right mt-3">
              <h5>总金额: <span id="total-amount">0.00</span> 元</h5>
            </div>
          </div>
        </div>

        <div class="text-center">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save"></i> 创建采购订单
          </button>
          <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
            <i class="fas fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- 食材选择模态框 -->
<div class="modal fade" id="ingredientModal" tabindex="-1" role="dialog" aria-labelledby="ingredientModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ingredientModalLabel">选择食材</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="form-group">
          <input type="text" class="form-control" id="ingredient-search" placeholder="搜索食材...">
        </div>
        <div class="table-responsive">
          <table class="table table-bordered" id="ingredients-table">
            <thead>
              <tr>
                <th>选择</th>
                <th>食材名称</th>
                <th>类别</th>
                <th>单位</th>
                <th>库存</th>
              </tr>
            </thead>
            <tbody>
              {% for ingredient in ingredients %}
              <tr data-id="{{ ingredient.id }}" data-name="{{ ingredient.name }}" data-unit="{{ ingredient.unit }}">
                <td><input type="checkbox" class="ingredient-checkbox"></td>
                <td>{{ ingredient.name }}</td>
                <td>{{ ingredient.category }}</td>
                <td>{{ ingredient.unit }}</td>
                <td>{{ ingredient.stock_quantity|default(0) }}</td>
              </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
        <button type="button" class="btn btn-primary" id="add-selected-ingredients">添加选中的食材</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
  $(document).ready(function() {
    // 计算总金额
    function calculateTotalAmount() {
      let total = 0;
      $('.ingredient-row').each(function() {
        const totalPrice = parseFloat($(this).find('input[name$="total_price"]').val()) || 0;
        total += totalPrice;
      });
      $('#total-amount').text(total.toFixed(2));
    }

    // 更新空食材提示的显示状态
    function updateNoIngredientsMessage() {
      if ($('.ingredient-row').length === 0) {
        $('#no-ingredients-message').show();
      } else {
        $('#no-ingredients-message').hide();
      }
    }

    // 计算行总价
    function calculateRowTotal(row) {
      const quantity = parseFloat($(row).find('.quantity-input').val()) || 0;
      const unitPrice = parseFloat($(row).find('.unit-price-input').val()) || 0;
      const totalPrice = quantity * unitPrice;
      $(row).find('input[name$="total_price"]').val(totalPrice.toFixed(2));
      calculateTotalAmount();
    }

    // 初始计算总金额和更新提示信息
    calculateTotalAmount();
    updateNoIngredientsMessage();

    // 监听数量和单价变化
    $(document).on('input', '.quantity-input, .unit-price-input', function() {
      calculateRowTotal($(this).closest('.ingredient-row'));
    });

    // 添加食材按钮
    $('#add-ingredient').click(function() {
      $('#ingredientModal').modal('show');
    });

    // 移除食材按钮
    $(document).on('click', '.remove-ingredient', function() {
      $(this).closest('.ingredient-row').remove();
      calculateTotalAmount();
      updateNoIngredientsMessage();
    });

    // 食材搜索
    $('#ingredient-search').on('keyup', function() {
      const value = $(this).val().toLowerCase();
      $('#ingredients-table tbody tr').filter(function() {
        $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
      });
    });

    // 添加选中的食材
    $('#add-selected-ingredients').click(function() {
      const selectedIngredients = [];

      $('#ingredients-table tbody tr').each(function() {
        if ($(this).find('.ingredient-checkbox').prop('checked')) {
          selectedIngredients.push({
            id: $(this).data('id'),
            name: $(this).data('name'),
            unit: $(this).data('unit')
          });
        }
      });

      if (selectedIngredients.length > 0) {
        for (const ingredient of selectedIngredients) {
          addIngredientRow(ingredient);
        }
        $('#ingredientModal').modal('hide');
        // 重置复选框
        $('.ingredient-checkbox').prop('checked', false);
        updateNoIngredientsMessage();
      }
    });

    // 添加食材行
    function addIngredientRow(ingredient) {
      const index = $('.ingredient-row').length;
      const template = `
        <div class="ingredient-row">
          <div class="row">
            <div class="col-md-3">
              <label class="form-label">食材名称</label>
              <input type="text" class="form-control" name="items-${index}-ingredient_name" value="${ingredient.name}" readonly>
              <input type="hidden" name="items-${index}-ingredient_id" value="${ingredient.id}">
            </div>
            <div class="col-md-2">
              <label class="form-label">数量</label>
              <input type="number" class="form-control quantity-input" name="items-${index}-quantity" value="1" min="0.01" step="0.01">
            </div>
            <div class="col-md-1">
              <label class="form-label">单位</label>
              <input type="text" class="form-control" name="items-${index}-unit" value="${ingredient.unit}" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">单价</label>
              <input type="number" class="form-control unit-price-input" name="items-${index}-unit_price" value="0" min="0" step="0.01">
            </div>
            <div class="col-md-2">
              <label class="form-label">总价</label>
              <input type="text" class="form-control" name="items-${index}-total_price" value="0.00" readonly>
            </div>
            <div class="col-md-2">
              <label class="form-label">&nbsp;</label>
              <button type="button" class="btn btn-danger btn-block remove-ingredient">
                <i class="fas fa-trash"></i> 移除
              </button>
            </div>
          </div>
          <div class="row mt-2">
            <div class="col-md-12">
              <label class="form-label">备注</label>
              <input type="text" class="form-control" name="items-${index}-notes">
            </div>
          </div>
          <input type="hidden" name="items-${index}-product_id" value="">
          <input type="hidden" name="items-${index}-supplier_id" value="">
        </div>
      `;

      $('#ingredients-container').append(template);
      calculateRowTotal($('.ingredient-row').last());
    }

    // 表单提交前验证
    $('#purchase-order-form').submit(function(e) {
      if ($('.ingredient-row').length === 0) {
        e.preventDefault();
        alert('请至少添加一种食材');
        return false;
      }

      // 检查数量和单价
      let valid = true;
      $('.ingredient-row').each(function() {
        const quantity = parseFloat($(this).find('.quantity-input').val()) || 0;
        const unitPrice = parseFloat($(this).find('.unit-price-input').val()) || 0;

        if (quantity <= 0) {
          valid = false;
          $(this).find('.quantity-input').addClass('is-invalid');
        } else {
          $(this).find('.quantity-input').removeClass('is-invalid');
        }

        if (unitPrice < 0) {
          valid = false;
          $(this).find('.unit-price-input').addClass('is-invalid');
        } else {
          $(this).find('.unit-price-input').removeClass('is-invalid');
        }
      });

      if (!valid) {
        e.preventDefault();
        alert('请检查数量和单价');
        return false;
      }
    });
  });
</script>
{% endblock %}
